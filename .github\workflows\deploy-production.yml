name: Production Deployment

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging

jobs:
  # Job 1: Build and Test
  build-and-test:
    name: Build & Test for Production
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm install
        cd frontend && npm install
    
    - name: Create environment files
      run: |
        # Backend environment
        cat > backend/.env << EOF
        DB_CONNECT_STRING=${{ secrets.MONGODB_URI }}
        JWT_KEY=${{ secrets.JWT_SECRET }}
        PORT=3002
        NODE_ENV=production
        GROQ_API_KEY=${{ secrets.GROQ_API_KEY }}
        REDIS_PASS=${{ secrets.REDIS_PASS }}
        SALT_ROUNDS=12
        TOKEN_EXPIRY=24h
        EOF
        
        # Frontend environment
        echo "VITE_API_URL=${{ secrets.VITE_API_URL }}" > frontend/.env
    
    - name: Run tests
      run: |
        npm test --if-present
        cd frontend && npm test --if-present
    
    - name: Build applications
      run: |
        cd frontend
        npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: production-build
        path: |
          frontend/dist/
          backend/
        retention-days: 30

  # Job 2: Deploy Frontend to Vercel
  deploy-frontend:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    needs: build-and-test
    environment:
      name: ${{ github.event.inputs.environment || 'production' }}
      url: ${{ steps.deploy.outputs.preview-url }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: production-build
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Install frontend dependencies
      run: cd frontend && npm ci
    
    - name: Build frontend
      run: |
        cd frontend
        echo "VITE_API_URL=${{ secrets.VITE_API_URL }}" > .env
        npm run build
    
    - name: Deploy to Vercel
      id: deploy
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./frontend
        vercel-args: ${{ github.event.inputs.environment == 'production' && '--prod' || '' }}
        scope: ${{ secrets.VERCEL_ORG_ID }}

  # Job 3: Deploy Backend to Railway/Render
  deploy-backend:
    name: Deploy Backend
    runs-on: ubuntu-latest
    needs: build-and-test
    environment:
      name: ${{ github.event.inputs.environment || 'production' }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to Railway
      uses: railwayapp/railway-deploy@v1.1.0
      with:
        railway-token: ${{ secrets.RAILWAY_TOKEN }}
        service: ${{ secrets.RAILWAY_SERVICE_ID }}
        environment: ${{ github.event.inputs.environment || 'production' }}

  # Job 4: Health Check
  health-check:
    name: Post-Deployment Health Check
    runs-on: ubuntu-latest
    needs: [deploy-frontend, deploy-backend]
    
    steps:
    - name: Wait for deployment
      run: sleep 60
    
    - name: Check frontend health
      run: |
        curl -f ${{ secrets.FRONTEND_URL }}/health || exit 1
    
    - name: Check backend health
      run: |
        curl -f ${{ secrets.BACKEND_URL }}/health || exit 1
    
    - name: Run smoke tests
      run: |
        # Test user registration endpoint
        response=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
          -H "Content-Type: application/json" \
          -d '{"email":"<EMAIL>","password":"TestPass123!","username":"testuser"}' \
          ${{ secrets.BACKEND_URL }}/user/register)
        
        if [ $response -eq 400 ] || [ $response -eq 409 ]; then
          echo "✅ Registration endpoint working (expected 400/409 for existing user)"
        else
          echo "❌ Registration endpoint returned unexpected status: $response"
          exit 1
        fi

  # Job 5: Notify on Success/Failure
  notify:
    name: Notification
    runs-on: ubuntu-latest
    needs: [deploy-frontend, deploy-backend, health-check]
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.health-check.result == 'success'
      run: |
        echo "🎉 Deployment successful!"
        echo "Frontend: ${{ secrets.FRONTEND_URL }}"
        echo "Backend: ${{ secrets.BACKEND_URL }}"
    
    - name: Notify on failure
      if: failure()
      run: |
        echo "❌ Deployment failed!"
        echo "Check the logs for details."
