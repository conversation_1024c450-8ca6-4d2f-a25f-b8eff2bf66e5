name: Maintenance & Security

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  # Job 1: Dependency Updates
  dependency-update:
    name: Update Dependencies
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Update backend dependencies
      run: |
        npm update
        npm audit fix --force || true
    
    - name: Update frontend dependencies
      run: |
        cd frontend
        npm update
        npm audit fix --force || true
    
    - name: Check for outdated packages
      run: |
        echo "=== Backend Outdated Packages ==="
        npm outdated || true
        echo "=== Frontend Outdated Packages ==="
        cd frontend && npm outdated || true
    
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update dependencies'
        title: '🔄 Automated Dependency Updates'
        body: |
          ## 🔄 Automated Dependency Updates
          
          This PR contains automated dependency updates:
          
          ### Changes
          - Updated npm packages to latest compatible versions
          - Applied security fixes where available
          
          ### Testing
          - [ ] Backend tests pass
          - [ ] Frontend builds successfully
          - [ ] No breaking changes detected
          
          **Note**: This PR was created automatically. Please review changes before merging.
        branch: chore/dependency-updates
        delete-branch: true

  # Job 2: Security Scan
  security-scan:
    name: Security Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Install dependencies
      run: |
        npm install
        cd frontend && npm install
    
    - name: Run npm audit (Backend)
      run: |
        echo "=== Backend Security Audit ==="
        npm audit --audit-level=moderate --json > backend-audit.json || true
        npm audit --audit-level=moderate || true
    
    - name: Run npm audit (Frontend)
      run: |
        echo "=== Frontend Security Audit ==="
        cd frontend
        npm audit --audit-level=moderate --json > ../frontend-audit.json || true
        npm audit --audit-level=moderate || true
    
    - name: Upload audit results
      uses: actions/upload-artifact@v4
      with:
        name: security-audit-results
        path: |
          backend-audit.json
          frontend-audit.json
        retention-days: 30

  # Job 3: Code Quality Check
  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Install dependencies
      run: |
        npm ci
        cd frontend && npm ci
    
    - name: Run ESLint (Frontend)
      run: |
        cd frontend
        npx eslint . --ext .ts,.tsx,.js,.jsx --format json --output-file ../eslint-report.json || true
        npx eslint . --ext .ts,.tsx,.js,.jsx || true
      continue-on-error: true
    
    - name: Check TypeScript compilation
      run: |
        cd frontend
        npx tsc --noEmit || true
      continue-on-error: true
    
    - name: Upload code quality reports
      uses: actions/upload-artifact@v4
      with:
        name: code-quality-reports
        path: |
          eslint-report.json
        retention-days: 30

  # Job 4: Performance Test
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Install dependencies
      run: |
        npm ci
        cd frontend && npm ci
    
    - name: Build frontend
      run: |
        cd frontend
        echo "VITE_API_URL=http://localhost:3002" > .env
        npm run build
    
    - name: Analyze bundle size
      run: |
        cd frontend
        npx bundlesize || true
      continue-on-error: true
    
    - name: Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        configPath: './.github/lighthouse/lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true
      continue-on-error: true

  # Job 5: Database Health Check
  database-health:
    name: Database Health Check
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Database connection test
      run: |
        node -e "
        require('dotenv').config({ path: './backend/.env' });
        const mongoose = require('mongoose');
        
        async function testConnection() {
          try {
            await mongoose.connect(process.env.DB_CONNECT_STRING || 'mongodb://localhost:27017/dsa-chatbot');
            console.log('✅ Database connection successful');
            await mongoose.disconnect();
          } catch (error) {
            console.error('❌ Database connection failed:', error.message);
            process.exit(1);
          }
        }
        
        testConnection();
        "
      env:
        DB_CONNECT_STRING: ${{ secrets.MONGODB_URI }}
