{"name": "dsa-chatbot", "version": "1.0.0", "description": "AI-powered Data Structures & Algorithms learning platform with intelligent chatbot assistance", "main": "backend/src/index.js", "scripts": {"start": "node backend/src/index.js", "dev": "nodemon backend/src/index.js", "build": "echo 'Build completed - ready for deployment'", "test": "mocha backend/test/**/*.test.js --timeout 10000", "test:watch": "mocha backend/test/**/*.test.js --watch", "clean": "rm -rf node_modules/.cache", "postinstall": "cd frontend && npm install"}, "keywords": ["dsa", "data-structures", "algorithms", "chatbot", "ai", "learning", "education", "coding", "interview-prep", "react", "nodejs", "mongodb"], "author": "DSA ChatBot Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/amanverma-00/Dsa-Mitra.git"}, "bugs": {"url": "https://github.com/amanverma-00/Dsa-Mitra/issues"}, "homepage": "https://github.com/amanverma-00/Dsa-Mitra#readme", "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "groq-sdk": "^0.27.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4", "node-fetch": "^3.3.2", "openai": "^5.10.1", "redis": "^5.6.0", "validator": "^13.15.15"}, "devDependencies": {"mocha": "^10.2.0", "nodemon": "^3.0.1"}}