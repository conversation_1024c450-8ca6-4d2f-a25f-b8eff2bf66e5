# Deployment Guide for DSA Chatbot

## Backend Deployment to Vercel

### Prerequisites
1. **MongoDB Atlas Account**: Create a free cluster at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. **Vercel Account**: Sign up at [Vercel](https://vercel.com)
3. **GROQ API Key**: Get your API key from [GROQ Console](https://console.groq.com)

### Step 1: Set up MongoDB Atlas
1. Create a new cluster in MongoDB Atlas
2. Create a database user with read/write permissions
3. Get your connection string (replace `<password>` with your actual password)
4. Whitelist Vercel's IP addresses or use `0.0.0.0/0` for all IPs

### Step 2: Deploy Backend to Vercel
1. **Install Vercel CLI** (if not already installed):
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Deploy the backend**:
   ```bash
   vercel --prod
   ```

4. **Set Environment Variables** in Vercel Dashboard:
   - Go to your project settings in Vercel
   - Add these environment variables:
     - `DB_CONNECT_STRING`: Your MongoDB Atlas connection string
     - `JWT_KEY`: A strong secret key for JWT tokens
     - `GROQ_API_KEY`: Your GROQ API key
     - `NODE_ENV`: `production`
     - `SALT_ROUNDS`: `12`
     - `TOKEN_EXPIRY`: `24h`

### Step 3: Update Frontend Configuration
1. Update `frontend/.env.production` to use your deployed backend:
   ```env
   VITE_API_URL=https://your-backend-url.vercel.app
   VITE_USE_MOCK_API=false
   ```

2. Redeploy frontend:
   ```bash
   cd frontend
   npm run build
   vercel --prod
   ```

### Step 4: Test the Deployment
1. Visit your frontend URL
2. Try signing up/logging in
3. Test the chat functionality
4. Check browser console for any errors

## Environment Variables Reference

### Required for Backend:
- `DB_CONNECT_STRING`: MongoDB connection string
- `JWT_KEY`: Secret key for JWT tokens
- `GROQ_API_KEY`: API key for GROQ AI service
- `NODE_ENV`: Set to `production`
- `SALT_ROUNDS`: Number of salt rounds for bcrypt (recommended: 12)
- `TOKEN_EXPIRY`: JWT token expiry time (e.g., `24h`)

### Optional:
- `REDIS_PASS`: Redis password (if using Redis for caching)
- `PORT`: Port number (Vercel handles this automatically)

## Troubleshooting

### Common Issues:
1. **CORS Errors**: Make sure your frontend domain is added to the CORS configuration
2. **Database Connection**: Verify MongoDB Atlas connection string and IP whitelist
3. **Environment Variables**: Double-check all required environment variables are set in Vercel
4. **API Key Issues**: Ensure GROQ API key is valid and has sufficient credits

### Logs:
- Check Vercel function logs in the Vercel dashboard
- Use `vercel logs` command to see recent logs
