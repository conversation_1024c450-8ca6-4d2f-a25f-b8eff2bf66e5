import express from "express";
import cors from "cors";
import { handleDemo } from "./routes/demo";

export function createServer() {
  const app = express();

  // Middleware
  app.use(cors());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Health check endpoint
  app.get("/health", (_req, res) => {
    res.json({ status: "ok", message: "Frontend server is running", timestamp: new Date().toISOString() });
  });

  // Example API routes
  app.get("/api/ping", (_req, res) => {
    res.json({ message: "Hello from Express server v2!" });
  });

  app.get("/api/demo", handleDemo);

  // Mock authentication endpoints for frontend-only deployment
  app.post("/user/register", (_req, res) => {
    res.json({
      success: true,
      user: { _id: "demo-user-id", username: "demo-user", email: "<EMAIL>" },
      token: "demo-jwt-token"
    });
  });

  app.post("/user/login", (_req, res) => {
    res.json({
      success: true,
      user: { _id: "demo-user-id", username: "demo-user", email: "<EMAIL>" },
      token: "demo-jwt-token"
    });
  });

  app.post("/user/logout", (_req, res) => {
    res.json({ success: true, message: "Logged out successfully" });
  });

  app.get("/user/getProfile", (_req, res) => {
    res.json({
      user: {
        _id: "demo-user-id",
        username: "demo-user",
        email: "<EMAIL>",
        role: "user",
        createdAt: new Date().toISOString(),
        learningPreferences: { difficulty: "beginner", language: "JavaScript" },
        stats: { totalMessages: 42, totalTokens: 1337, uniqueConcepts: 15 },
        activeSessions: 1,
        sessions: []
      }
    });
  });

  return app;
}
