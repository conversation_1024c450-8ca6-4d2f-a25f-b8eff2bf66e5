version: 2
updates:
  # Backend dependencies
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "amanverma-00"
    assignees:
      - "amanverma-00"
    commit-message:
      prefix: "chore(backend)"
      include: "scope"
    labels:
      - "dependencies"
      - "backend"
    ignore:
      # Ignore major version updates for critical packages
      - dependency-name: "express"
        update-types: ["version-update:semver-major"]
      - dependency-name: "mongoose"
        update-types: ["version-update:semver-major"]

  # Frontend dependencies
  - package-ecosystem: "npm"
    directory: "/frontend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "amanverma-00"
    assignees:
      - "amanverma-00"
    commit-message:
      prefix: "chore(frontend)"
      include: "scope"
    labels:
      - "dependencies"
      - "frontend"
    ignore:
      # Ignore major version updates for critical packages
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "vite"
        update-types: ["version-update:semver-major"]

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "amanverma-00"
    assignees:
      - "amanverma-00"
    commit-message:
      prefix: "chore(ci)"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
