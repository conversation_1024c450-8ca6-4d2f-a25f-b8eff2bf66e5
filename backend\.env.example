# Database Configuration
DB_CONNECT_STRING=mongodb://localhost:27017/dsa-chatbot

# JWT Secret - Change this in production!
JWT_KEY=your-super-secret-jwt-key-change-this-in-production

# Server Configuration
PORT=3002
NODE_ENV=development

# GROQ API Configuration
# Get your free API key from https://console.groq.com
GROQ_API_KEY=your-groq-api-key-here

# Redis Configuration (optional)
REDIS_PASS=your-redis-password-here

# Salt rounds for bcrypt
SALT_ROUNDS=10

# Token expiry
TOKEN_EXPIRY=24h
