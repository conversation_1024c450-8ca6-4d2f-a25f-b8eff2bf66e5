const mongoose = require('mongoose');

async function main() {
    try {
        if (!process.env.DB_CONNECT_STRING) {
            throw new Error('DB_CONNECT_STRING environment variable is not set');
        }

        console.log('Attempting to connect to MongoDB...');
        await mongoose.connect(process.env.DB_CONNECT_STRING, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ Database connected successfully');
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        throw error;
    }
}

module.exports = main;