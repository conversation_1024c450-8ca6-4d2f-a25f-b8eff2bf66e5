# Dependencies
node_modules/
frontend/node_modules/
backend/node_modules/

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
backend/.env
frontend/.env

# Build outputs
dist/
build/
frontend/dist/
backend/dist/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Documentation build
docs/_build/

# Local development files
.local
*.local

# Test files
test-results/
playwright-report/
test-results.xml

# Deployment files
.netlify/
.vercel/

# Docker
.dockerignore
Dockerfile
docker-compose.yml

# Package manager lock files (optional - you may want to commit these)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Development scripts and temporary files
kill-port.*
start-dev.js
test-*.js
*-test.js
*.test.local.js

# Documentation files from development
*_COMPLETE.md
*_FIXED.md
*_REPORT.md
QUICK_TEST_GUIDE.md
GROQ_API_SETUP.md

.vercel
