/**
 * DSA Chatbot API Types and Service
 * Shared code between client and server
 */

// Base API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_URL || window.location.origin;

// Smart API mode detection
export const USE_MOCK_API = (() => {
  // Explicitly set to use mock API
  if (import.meta.env.VITE_USE_MOCK_API === 'true') {
    return true;
  }

  // Explicitly set to use real API
  if (import.meta.env.VITE_USE_MOCK_API === 'false') {
    return false;
  }

  // Auto-detect: Use mock API if no backend URL is configured or if it's the same as frontend
  if (typeof window !== 'undefined') {
    const apiUrl = import.meta.env.VITE_API_URL;
    const currentOrigin = window.location.origin;

    // If no API URL is set or it's the same as frontend origin, use mock
    if (!apiUrl || apiUrl === currentOrigin) {
      return true;
    }
  }

  // Default to real API
  return false;
})();

// User Types
export interface User {
  _id: string;
  username: string;
  email: string;
  role: 'user' | 'assistant';
  createdAt: string;
  lastActive?: string;
  learningPreferences: {
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    language: string;
  };
  stats: {
    totalMessages: number;
    totalTokens: number;
    uniqueConcepts: number;
  };
  activeSessions: number;
  sessions: SessionSummary[];
}

export interface SessionSummary {
  id: string;
  title: string;
  created: string;
  lastActive: string;
  messageCount?: number;
  tokensUsed?: number;
  isActive?: boolean;
}

export interface Session {
  id: string;
  title: string;
  context: {
    name?: string;
    currentTopic?: string;
    difficultyLevel: 'beginner' | 'intermediate' | 'advanced';
    lastConcept?: string;
  };
  createdAt: string;
  lastActiveAt: string;
  isActive: boolean;
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: string;
  metadata?: {
    tokensUsed?: number;
    modelVersion?: string;
    isDSAConcept?: boolean;
    conceptTags?: string[];
  };
}

export interface LearningProgress {
  concept: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  masteryLevel: 'introduced' | 'practicing' | 'comfortable' | 'mastered';
  firstEncountered: string;
  lastPracticed: string;
  practiceCount: number;
  sessions: Array<{
    sessionId: string;
    discussedAt: string;
  }>;
}

export interface LearningStats {
  totalConcepts: number;
  masteredConcepts: number;
  categoriesLearned: string[];
  totalPracticeCount: number;
}

// API Response Types
export interface AuthResponse {
  user: User;
  message: string;
}

export interface ProfileResponse {
  user: User;
}

export interface SessionResponse {
  session: Session;
  messages: Message[];
}

export interface SessionsResponse {
  sessions: SessionSummary[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalSessions: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface MessageResponse {
  userMessage: Message;
  assistantMessage: Message;
}

export interface LearningProgressResponse {
  progress: LearningProgress[];
  stats: LearningStats;
}

export interface ExportResponse {
  user: User;
  sessions: Session[];
  messages: Message[];
  learningProgress: LearningProgress[];
  exportedAt: string;
}

// API Error Type
export interface ApiError {
  error: string;
  details?: string;
}

// Mock API responses for frontend-only deployment
const mockResponses = {
  '/health': { status: 'ok', message: 'Mock API is running' },
  '/user/register': {
    success: true,
    user: { _id: 'mock-user-id', username: 'demo-user', email: '<EMAIL>' },
    token: 'mock-jwt-token'
  },
  '/user/login': {
    success: true,
    user: { _id: 'mock-user-id', username: 'demo-user', email: '<EMAIL>' },
    token: 'mock-jwt-token'
  },
  '/user/logout': { success: true, message: 'Logged out successfully' },
  '/user/getProfile': {
    user: {
      _id: 'mock-user-id',
      username: 'demo-user',
      email: '<EMAIL>',
      role: 'user',
      createdAt: new Date().toISOString(),
      learningPreferences: { difficulty: 'beginner', language: 'JavaScript' },
      stats: { totalMessages: 42, totalTokens: 1337, uniqueConcepts: 15 },
      activeSessions: 1,
      sessions: []
    }
  },
  '/api/profile': {
    user: {
      _id: 'mock-user-id',
      username: 'demo-user',
      email: '<EMAIL>',
      role: 'user',
      createdAt: new Date().toISOString(),
      learningPreferences: { difficulty: 'beginner', language: 'JavaScript' },
      stats: { totalMessages: 42, totalTokens: 1337, uniqueConcepts: 15 },
      activeSessions: 1,
      sessions: []
    }
  },
  '/api/sessions': {
    sessionId: 'mock-session-id',
    title: 'Demo Session',
    createdAt: new Date().toISOString()
  },
  '/api/profile/sessions': {
    sessions: [
      {
        id: 'mock-session-1',
        title: 'Arrays and Strings',
        created: new Date(Date.now() - 86400000).toISOString(),
        lastActive: new Date().toISOString(),
        messageCount: 15,
        tokensUsed: 450,
        isActive: true
      }
    ],
    pagination: {
      currentPage: 1,
      totalPages: 1,
      totalSessions: 1,
      hasNext: false,
      hasPrev: false
    }
  },
  '/api/profile/learning-progress': {
    progress: [
      {
        concept: 'Arrays',
        category: 'Data Structures',
        difficulty: 'beginner',
        masteryLevel: 'comfortable',
        firstEncountered: new Date(Date.now() - 604800000).toISOString(),
        lastPracticed: new Date().toISOString(),
        practiceCount: 5,
        sessions: [{ sessionId: 'mock-session-1', discussedAt: new Date().toISOString() }]
      }
    ],
    stats: {
      totalConcepts: 8,
      masteredConcepts: 3,
      categoriesLearned: ['Data Structures', 'Algorithms'],
      totalPracticeCount: 25
    }
  }
};

// API Service Class
export class DSAApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async mockRequest<T>(endpoint: string): Promise<T> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Handle exact matches first
    const response = mockResponses[endpoint as keyof typeof mockResponses];
    if (response) {
      return response as T;
    }

    // Handle dynamic endpoints
    if (endpoint.startsWith('/api/sessions/') && endpoint.endsWith('/messages')) {
      // Mock sending a message
      const sessionId = endpoint.split('/')[3];
      return {
        userMessage: {
          id: 'mock-user-msg-' + Date.now(),
          content: 'User message',
          role: 'user',
          timestamp: new Date().toISOString()
        },
        assistantMessage: {
          id: 'mock-assistant-msg-' + Date.now(),
          content: 'This is a demo response from the DSA Chatbot. In a real implementation, this would be powered by an AI model that helps you learn Data Structures and Algorithms!',
          role: 'assistant',
          timestamp: new Date().toISOString(),
          metadata: {
            tokensUsed: 45,
            modelVersion: 'demo-v1',
            isDSAConcept: true,
            conceptTags: ['demo', 'introduction']
          }
        }
      } as T;
    }

    if (endpoint.startsWith('/api/sessions/')) {
      // Mock getting a session
      const sessionId = endpoint.split('/')[3];
      return {
        session: {
          id: sessionId,
          title: 'Demo Session',
          context: {
            name: 'Demo User',
            currentTopic: 'Arrays',
            difficultyLevel: 'beginner',
            lastConcept: 'Array Traversal'
          },
          createdAt: new Date().toISOString(),
          lastActiveAt: new Date().toISOString(),
          isActive: true
        },
        messages: [
          {
            id: 'welcome-msg',
            content: 'Welcome to your DSA learning session! How can I help you today?',
            role: 'assistant',
            timestamp: new Date().toISOString()
          }
        ]
      } as T;
    }

    if (endpoint.startsWith('/api/delete-chat')) {
      return { success: true, message: 'Session deleted successfully' } as T;
    }

    if (endpoint.startsWith('/api/profile/export')) {
      return {
        user: mockResponses['/user/getProfile'].user,
        sessions: [],
        messages: [],
        learningProgress: [],
        exportedAt: new Date().toISOString()
      } as T;
    }

    console.warn(`Mock endpoint ${endpoint} not implemented, returning empty response`);
    return {} as T;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Use mock API if enabled
    if (USE_MOCK_API) {
      console.log(`🎭 Mock API: ${endpoint}`);
      return this.mockRequest<T>(endpoint);
    }

    const url = `${this.baseUrl}${endpoint}`;

    const config: RequestInit = {
      credentials: 'include', // Important for cookies
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Network error' }));

        // Handle authentication errors specifically
        if (response.status === 401) {
          throw new Error('Authentication required');
        }

        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      // Only log non-authentication errors to avoid console spam
      if (error instanceof Error && !error.message.includes('Authentication required')) {
        console.error(`API Error (${endpoint}):`, error);
      }
      throw error;
    }
  }

  // Authentication Methods
  async register(userData: {
    username: string;
    email: string;
    password: string;
  }): Promise<AuthResponse> {
    return this.request<AuthResponse>('/user/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async login(credentials: {
    email: string;
    password: string;
  }): Promise<AuthResponse> {
    return this.request<AuthResponse>('/user/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async logout(): Promise<{ success: boolean; message: string }> {
    return this.request('/user/logout', {
      method: 'POST',
    });
  }

  async deleteProfile(): Promise<{ message: string }> {
    return this.request('/user/deleteProfile', {
      method: 'DELETE',
    });
  }

  // Profile Methods
  async getProfile(): Promise<ProfileResponse> {
    return this.request<ProfileResponse>('/user/getProfile');
  }

  async getEnhancedProfile(): Promise<ProfileResponse> {
    return this.request<ProfileResponse>('/api/profile');
  }

  async updateProfile(updates: {
    username?: string;
    email?: string;
    learningPreferences?: {
      difficulty?: 'beginner' | 'intermediate' | 'advanced';
      language?: string;
    };
  }): Promise<{ user: User }> {
    return this.request('/api/profile', {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  // Session Methods
  async createSession(sessionData: {
    name?: string;
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
  }): Promise<{ sessionId: string; title: string; createdAt: string }> {
    return this.request('/api/sessions', {
      method: 'POST',
      body: JSON.stringify(sessionData),
    });
  }

  async getSession(sessionId: string): Promise<SessionResponse> {
    return this.request<SessionResponse>(`/api/sessions/${sessionId}`);
  }

  async getSessions(page: number = 1, limit: number = 10): Promise<SessionsResponse> {
    return this.request<SessionsResponse>(`/api/profile/sessions?page=${page}&limit=${limit}`);
  }

  async deleteSession(sessionId: string): Promise<{ success: boolean; message: string }> {
    return this.request(`/api/delete-chat`, {
      method: 'POST',
      body: JSON.stringify({
        sessionId: sessionId
      }),
    });
  }

  // Message Methods
  async sendMessage(sessionId: string, content: string): Promise<MessageResponse> {
    return this.request<MessageResponse>(`/api/sessions/${sessionId}/messages`, {
      method: 'POST',
      body: JSON.stringify({
        content,
        role: 'user',
      }),
    });
  }

  // Learning Progress Methods
  async getLearningProgress(): Promise<LearningProgressResponse> {
    return this.request<LearningProgressResponse>('/api/profile/learning-progress');
  }

  // Data Export
  async exportData(): Promise<ExportResponse> {
    return this.request<ExportResponse>('/api/profile/export', {
      method: 'POST',
    });
  }
}

// Create a singleton instance
export const apiService = new DSAApiService();
